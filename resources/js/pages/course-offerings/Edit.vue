<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import type { Campus, CourseOffering, Semester, Unit, User } from '@/types/models';
import { Head, Link, router } from '@inertiajs/vue3';
import { toTypedSchema } from '@vee-validate/zod';
import { ArrowLeft, Save } from 'lucide-vue-next';
import { useForm } from 'vee-validate';
import { z } from 'zod';

interface Props {
    courseOffering: CourseOffering;
    semesters: Semester[];
    units: Unit[];
    campuses: Campus[];
    instructors: User[];
}

const props = defineProps<Props>();

const breadcrumbItems = [
    { label: 'Course Offerings', href: '/course-offerings' },
    { label: 'Edit Course Offering', href: `/course-offerings/${props.courseOffering.id}/edit` },
];

// Define validation schema following development standards
const formSchema = toTypedSchema(
    z.object({
        semester_id: z.string().min(1, 'Semester is required'),
        unit_id: z.string().min(1, 'Unit is required'),
        instructor_id: z.string().optional(),
        section_code: z.string().max(10, 'Section code too long').optional(),
        max_capacity: z.number().int().min(1, 'Max capacity must be at least 1').max(500, 'Max capacity cannot exceed 500'),
        waitlist_capacity: z.number().int().min(0, 'Waitlist capacity must be 0 or greater').max(100, 'Waitlist capacity cannot exceed 100'),
        delivery_mode: z.enum(['in_person', 'online', 'hybrid', 'blended'], {
            errorMap: () => ({ message: 'Please select a delivery mode' }),
        }),
        schedule_days: z.array(z.enum(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'])).optional(),
        schedule_time_start: z.string().optional(),
        schedule_time_end: z.string().optional(),
        location: z.string().max(255, 'Location too long').optional(),
        enrollment_status: z.enum(['open', 'closed', 'waitlist_only', 'cancelled']),
        registration_start_date: z.string().optional(),
        registration_end_date: z.string().optional(),
        special_requirements: z.string().max(1000, 'Special requirements too long').optional(),
        notes: z.string().max(1000, 'Notes too long').optional(),
    }),
);
            .refine((val) => !isNaN(Number(val)) && Number(val) > 0, 'Max enrollment must be greater than 0'),
        waitlist_capacity: z
            .string()
            .refine((val) => val === '' || (!isNaN(Number(val)) && Number(val) >= 0), 'Waitlist capacity must be 0 or greater')
            .optional(),
        delivery_mode: z.enum(['in_person', 'online', 'hybrid'], {
            errorMap: () => ({ message: 'Please select a delivery mode' }),
        }),
        location: z.string().max(255, 'Location too long').optional(),
        instructor_id: z.string().optional(),
        registration_start_date: z.string().optional(),
        registration_end_date: z.string().optional(),
        drop_deadline: z.string().optional(),
        withdrawal_deadline: z.string().optional(),
        tuition_per_credit: z
            .string()
            .refine((val) => val === '' || (!isNaN(Number(val)) && Number(val) >= 0), 'Tuition per credit must be 0 or greater')
            .optional(),
        additional_fees: z
            .string()
            .refine((val) => val === '' || (!isNaN(Number(val)) && Number(val) >= 0), 'Additional fees must be 0 or greater')
            .optional(),
        notes: z.string().optional(),
    }),
);

// Helper function to format date for input
const formatDateForInput = (dateString: string | null): string => {
    if (!dateString) return '';
    return new Date(dateString).toISOString().split('T')[0];
};

const { handleSubmit, isSubmitting, setFieldValue } = useForm({
    validationSchema: formSchema,
    initialValues: {
        semester_id: props.courseOffering.semester_id.toString(),
        unit_id: props.courseOffering.unit_id.toString(),
        campus_id: props.courseOffering.campus_id.toString(),
        course_code: props.courseOffering.course_code || '',
        section_code: props.courseOffering.section_code || '',
        course_title: props.courseOffering.course_title || '',
        credit_hours: props.courseOffering.credit_hours.toString(),
        max_enrollment: props.courseOffering.max_enrollment.toString(),
        waitlist_capacity: props.courseOffering.waitlist_capacity?.toString() || '0',
        delivery_mode: props.courseOffering.delivery_mode,
        location: props.courseOffering.location || '',
        instructor_id: props.courseOffering.instructor_id?.toString() || 'none',
        registration_start_date: formatDateForInput(props.courseOffering.registration_start_date),
        registration_end_date: formatDateForInput(props.courseOffering.registration_end_date),
        drop_deadline: formatDateForInput(props.courseOffering.drop_deadline),
        withdrawal_deadline: formatDateForInput(props.courseOffering.withdrawal_deadline),
        tuition_per_credit: props.courseOffering.tuition_per_credit?.toString() || '0',
        additional_fees: props.courseOffering.additional_fees?.toString() || '0',
        notes: props.courseOffering.notes || '',
    },
});

// Auto-fill course details when unit is selected
const handleUnitChange = (unitId: any) => {
    if (!unitId) return;
    const selectedUnit = props.units.find((unit) => unit.id.toString() === unitId.toString());
    if (selectedUnit) {
        setFieldValue('course_code', selectedUnit.code);
        setFieldValue('course_title', selectedUnit.name);
        setFieldValue('credit_hours', selectedUnit.credit_points.toString());
    }
};

const onSubmit = handleSubmit((values) => {
    const formData = {
        ...values,
        credit_hours: Number(values.credit_hours),
        max_enrollment: Number(values.max_enrollment),
        waitlist_capacity: values.waitlist_capacity ? Number(values.waitlist_capacity) : 0,
        tuition_per_credit: values.tuition_per_credit ? Number(values.tuition_per_credit) : 0,
        additional_fees: values.additional_fees ? Number(values.additional_fees) : 0,
        instructor_id: values.instructor_id === 'none' ? null : values.instructor_id,
    };

    router.put(`/course-offerings/${props.courseOffering.id}`, formData, {
        onSuccess: () => {
            // Success handled by redirect
        },
        onError: (errors) => {
            console.error('Validation errors:', errors);
        },
    });
});

const deliveryModeOptions = [
    { value: 'in_person', label: 'In Person' },
    { value: 'online', label: 'Online' },
    { value: 'hybrid', label: 'Hybrid' },
];
</script>

<template>
    <Head title="Edit Course Offering" />
    <AppLayout :breadcrumbs="breadcrumbItems">
        <div class="h-full space-y-4 p-4">
            <!-- Header -->
            <div class="flex items-center gap-4">
                <Link href="/course-offerings">
                    <Button variant="outline" size="sm">
                        <ArrowLeft class="mr-2 h-4 w-4" />
                        Back to Course Offerings
                    </Button>
                </Link>
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Edit Course Offering</h1>
                    <p class="text-muted-foreground">Update course offering details and registration settings</p>
                </div>
            </div>

            <form @submit="onSubmit" class="space-y-6">
                <div class="grid gap-6 lg:grid-cols-2">
                    <!-- Basic Information -->
                    <Card>
                        <CardHeader>
                            <CardTitle>Basic Information</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <FormField v-slot="{ componentField }" name="semester_id">
                                <FormItem>
                                    <FormLabel>Semester</FormLabel>
                                    <FormControl>
                                        <Select v-bind="componentField">
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select semester" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="semester in semesters" :key="semester.id" :value="semester.id.toString()">
                                                    {{ semester.name }} ({{ semester.code }})
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>

                            <FormField v-slot="{ componentField }" name="unit_id">
                                <FormItem>
                                    <FormLabel>Unit</FormLabel>
                                    <FormControl>
                                        <Select v-bind="componentField" @update:model-value="handleUnitChange">
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select unit" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="unit in units" :key="unit.id" :value="unit.id.toString()">
                                                    {{ unit.code }} - {{ unit.name }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>

                            <FormField v-slot="{ componentField }" name="campus_id">
                                <FormItem>
                                    <FormLabel>Campus</FormLabel>
                                    <FormControl>
                                        <Select v-bind="componentField">
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select campus" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="campus in campuses" :key="campus.id" :value="campus.id.toString()">
                                                    {{ campus.name }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>

                            <div class="grid grid-cols-2 gap-4">
                                <FormField v-slot="{ componentField }" name="course_code">
                                    <FormItem>
                                        <FormLabel>Course Code</FormLabel>
                                        <FormControl>
                                            <Input v-bind="componentField" placeholder="e.g., CS101" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>

                                <FormField v-slot="{ componentField }" name="section_code">
                                    <FormItem>
                                        <FormLabel>Section Code</FormLabel>
                                        <FormControl>
                                            <Input v-bind="componentField" placeholder="e.g., A, B1" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>
                            </div>

                            <FormField v-slot="{ componentField }" name="course_title">
                                <FormItem>
                                    <FormLabel>Course Title</FormLabel>
                                    <FormControl>
                                        <Input v-bind="componentField" placeholder="Enter course title" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>
                        </CardContent>
                    </Card>

                    <!-- Enrollment & Delivery -->
                    <Card>
                        <CardHeader>
                            <CardTitle>Enrollment & Delivery</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <FormField v-slot="{ componentField }" name="credit_hours">
                                    <FormItem>
                                        <FormLabel>Credit Hours</FormLabel>
                                        <FormControl>
                                            <Input v-bind="componentField" type="number" step="0.5" min="0" max="10" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>

                                <FormField v-slot="{ componentField }" name="max_enrollment">
                                    <FormItem>
                                        <FormLabel>Max Enrollment</FormLabel>
                                        <FormControl>
                                            <Input v-bind="componentField" type="number" min="1" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>
                            </div>

                            <FormField v-slot="{ componentField }" name="waitlist_capacity">
                                <FormItem>
                                    <FormLabel>Waitlist Capacity</FormLabel>
                                    <FormControl>
                                        <Input v-bind="componentField" type="number" min="0" placeholder="0" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>

                            <FormField v-slot="{ componentField }" name="delivery_mode">
                                <FormItem>
                                    <FormLabel>Delivery Mode</FormLabel>
                                    <FormControl>
                                        <Select v-bind="componentField">
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select delivery mode" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem v-for="option in deliveryModeOptions" :key="option.value" :value="option.value">
                                                    {{ option.label }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>

                            <FormField v-slot="{ componentField }" name="location">
                                <FormItem>
                                    <FormLabel>Location</FormLabel>
                                    <FormControl>
                                        <Input v-bind="componentField" placeholder="e.g., Room 101, Online" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>

                            <FormField v-slot="{ componentField }" name="instructor_id">
                                <FormItem>
                                    <FormLabel>Instructor</FormLabel>
                                    <FormControl>
                                        <Select v-bind="componentField">
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select instructor (optional)" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="none">No instructor assigned</SelectItem>
                                                <SelectItem v-for="instructor in instructors" :key="instructor.id" :value="instructor.id.toString()">
                                                    {{ instructor.name }}
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>
                        </CardContent>
                    </Card>

                    <!-- Registration Dates -->
                    <Card>
                        <CardHeader>
                            <CardTitle>Registration Dates</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <FormField v-slot="{ componentField }" name="registration_start_date">
                                    <FormItem>
                                        <FormLabel>Registration Start Date</FormLabel>
                                        <FormControl>
                                            <Input v-bind="componentField" type="date" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>

                                <FormField v-slot="{ componentField }" name="registration_end_date">
                                    <FormItem>
                                        <FormLabel>Registration End Date</FormLabel>
                                        <FormControl>
                                            <Input v-bind="componentField" type="date" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>
                            </div>

                            <div class="grid grid-cols-2 gap-4">
                                <FormField v-slot="{ componentField }" name="drop_deadline">
                                    <FormItem>
                                        <FormLabel>Drop Deadline</FormLabel>
                                        <FormControl>
                                            <Input v-bind="componentField" type="date" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>

                                <FormField v-slot="{ componentField }" name="withdrawal_deadline">
                                    <FormItem>
                                        <FormLabel>Withdrawal Deadline</FormLabel>
                                        <FormControl>
                                            <Input v-bind="componentField" type="date" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>
                            </div>
                        </CardContent>
                    </Card>

                    <!-- Financial Information -->
                    <Card>
                        <CardHeader>
                            <CardTitle>Financial Information</CardTitle>
                        </CardHeader>
                        <CardContent class="space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <FormField v-slot="{ componentField }" name="tuition_per_credit">
                                    <FormItem>
                                        <FormLabel>Tuition per Credit</FormLabel>
                                        <FormControl>
                                            <Input v-bind="componentField" type="number" step="0.01" min="0" placeholder="0.00" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>

                                <FormField v-slot="{ componentField }" name="additional_fees">
                                    <FormItem>
                                        <FormLabel>Additional Fees</FormLabel>
                                        <FormControl>
                                            <Input v-bind="componentField" type="number" step="0.01" min="0" placeholder="0.00" />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                </FormField>
                            </div>

                            <FormField v-slot="{ componentField }" name="notes">
                                <FormItem>
                                    <FormLabel>Notes</FormLabel>
                                    <FormControl>
                                        <Textarea v-bind="componentField" placeholder="Additional notes or requirements..." rows="3" />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            </FormField>
                        </CardContent>
                    </Card>
                </div>

                <!-- Actions -->
                <div class="flex justify-end gap-4">
                    <Link href="/course-offerings">
                        <Button type="button" variant="outline">Cancel</Button>
                    </Link>
                    <Button type="submit" :disabled="isSubmitting">
                        <Save class="mr-2 h-4 w-4" />
                        {{ isSubmitting ? 'Updating...' : 'Update Course Offering' }}
                    </Button>
                </div>
            </form>
        </div>
    </AppLayout>
</template>
