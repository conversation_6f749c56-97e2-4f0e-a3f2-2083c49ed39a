<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class CourseOffering extends Model
{
    /** @use HasFactory<\Database\Factories\CourseOfferingFactory> */
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'semester_id',
        'unit_id',
        'instructor_id',
        'section_code',
        'max_capacity',
        'current_enrollment',
        'waitlist_capacity',
        'current_waitlist',
        'delivery_mode',
        'schedule_days',
        'schedule_time_start',
        'schedule_time_end',
        'location',
        'is_active',
        'enrollment_status',
        'registration_start_date',
        'registration_end_date',
        'special_requirements',
        'notes',
    ];

    protected $casts = [
        'max_capacity' => 'integer',
        'current_enrollment' => 'integer',
        'waitlist_capacity' => 'integer',
        'current_waitlist' => 'integer',
        'schedule_time_start' => 'datetime',
        'schedule_time_end' => 'datetime',
        'is_active' => 'boolean',
        'schedule_days' => 'array',
        'registration_start_date' => 'date',
        'registration_end_date' => 'date',
    ];

    // Relationships
    public function semester(): BelongsTo
    {
        return $this->belongsTo(Semester::class);
    }

    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    public function instructor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'instructor_id');
    }

    public function studentEnrollments(): HasMany
    {
        return $this->hasMany(StudentUnitEnrollment::class, 'course_offering_id');
    }

    // Helper methods
    public function getAvailableSpots(): int
    {
        return max(0, $this->max_capacity - $this->current_enrollment);
    }

    public function getAvailableWaitlistSpots(): int
    {
        return max(0, $this->waitlist_capacity - $this->current_waitlist);
    }

    public function isFull(): bool
    {
        return $this->current_enrollment >= $this->max_capacity;
    }

    public function isWaitlistFull(): bool
    {
        return $this->current_waitlist >= $this->waitlist_capacity;
    }

    public function canEnroll(): bool
    {
        return $this->is_active
            && $this->enrollment_status === 'open'
            && !$this->isFull()
            && $this->isRegistrationOpen();
    }

    public function canJoinWaitlist(): bool
    {
        return $this->is_active
            && in_array($this->enrollment_status, ['open', 'waitlist_only'])
            && $this->isFull()
            && !$this->isWaitlistFull()
            && $this->isRegistrationOpen();
    }

    public function isRegistrationOpen(): bool
    {
        $now = Carbon::now()->toDateString();

        $startOk = !$this->registration_start_date || $this->registration_start_date <= $now;
        $endOk = !$this->registration_end_date || $this->registration_end_date >= $now;

        return $startOk && $endOk;
    }

    public function getEnrollmentStatusText(): string
    {
        if (!$this->isRegistrationOpen()) {
            return 'Registration Closed';
        }

        return match($this->enrollment_status) {
            'open' => $this->isFull() ? 'Full - Waitlist Available' : 'Open',
            'closed' => 'Closed',
            'waitlist_only' => 'Waitlist Only',
            'cancelled' => 'Cancelled',
            default => 'Unknown'
        };
    }

    // Scopes
    public function scopeActive(Builder $query): void
    {
        $query->where('is_active', true);
    }

    public function scopeCurrentSemester(Builder $query): void
    {
        $query->whereHas('semester', function ($q) {
            $q->where('is_active', true);
        });
    }

    public function scopeForSemester(Builder $query, int $semesterId): void
    {
        $query->where('semester_id', $semesterId);
    }

    public function scopeForUnit(Builder $query, int $unitId): void
    {
        $query->where('unit_id', $unitId);
    }

    public function scopeByInstructor(Builder $query, int $instructorId): void
    {
        $query->where('instructor_id', $instructorId);
    }

    public function scopeAvailable(Builder $query): void
    {
        $query->where('is_active', true)
            ->where('enrollment_status', 'open')
            ->whereRaw('current_enrollment < max_capacity');
    }

    public function scopeFull(Builder $query): void
    {
        $query->whereRaw('current_enrollment >= max_capacity');
    }

    public function scopeByDeliveryMode(Builder $query, string $mode): void
    {
        $query->where('delivery_mode', $mode);
    }

    public function scopeWithWaitlist(Builder $query): void
    {
        $query->where('waitlist_capacity', '>', 0);
    }

    public function scopeRegistrationOpen(Builder $query): void
    {
        $now = Carbon::now()->toDateString();
        $query->where(function ($q) use ($now) {
            $q->whereNull('registration_start_date')
              ->orWhere('registration_start_date', '<=', $now);
        })->where(function ($q) use ($now) {
            $q->whereNull('registration_end_date')
              ->orWhere('registration_end_date', '>=', $now);
        });
    }

    public function scopeEnrollable(Builder $query): void
    {
        $query->active()
            ->where('enrollment_status', 'open')
            ->registrationOpen()
            ->whereRaw('current_enrollment < max_capacity');
    }
}
